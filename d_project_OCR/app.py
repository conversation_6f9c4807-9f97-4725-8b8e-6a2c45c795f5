from flask import Flask, request, jsonify, render_template, send_from_directory
import os
import json
import uuid
from PIL import Image
import torch
import time
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText

app = Flask(__name__)

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

app.config['UPLOAD_FOLDER'] = config['upload_folder']
app.config['MAX_CONTENT_LENGTH'] = config['max_file_size']

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize OCR model
class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        """Initialize OCR model"""
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.processor = AutoProcessor.from_pretrained(model_path)

    def extract_document_text_smart(self, image: Image.Image) -> str:
        """Enhanced OCR for documents"""
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing...")

            prompt = (
                "Extract all visible text from this image exactly as it appears. "
                "Preserve indentation, line breaks, symbols, and formatting. Do not summarize. "
                "Return only the raw plain text output. Do not add extra notes or explanations."
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(
                text=[text], images=[image], padding=True, return_tensors="pt"
            ).to(self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True, clean_up_tokenization_spaces=True)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return output_text[0]

        except Exception as e:
            print(f"❌ Error in OCR: {e}")
            return f"Error processing image: {str(e)}"

# Initialize OCR engine with error handling
ocr_engine = None
try:
    print("🔄 Loading OCR model...")
    ocr_engine = DocumentOCR(config['model_path'])
    print("✅ OCR model loaded successfully!")
except Exception as e:
    print(f"❌ Failed to load OCR model: {e}")
    print("⚠️  The web interface will still work, but OCR processing will be disabled.")

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'ocr_model_loaded': ocr_engine is not None
    })

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = str(uuid.uuid4()) + '.' + file.filename.rsplit('.', 1)[1].lower()
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'url': f'/uploads/{filename}'
        })
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/process', methods=['POST'])
def process_image():
    data = request.get_json()
    filename = data.get('filename')

    if not filename:
        return jsonify({'error': 'No filename provided'}), 400

    if ocr_engine is None:
        return jsonify({'error': 'OCR model not available. Please check server logs.'}), 503

    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

    if not os.path.exists(filepath):
        return jsonify({'error': 'File not found'}), 404

    try:
        # Load and process image
        image = Image.open(filepath).convert("RGB")
        result = ocr_engine.extract_document_text_smart(image)

        return jsonify({
            'success': True,
            'text': result
        })

    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in config['allowed_extensions']

if __name__ == '__main__':
    app.run(
        host=config['host'],
        port=config['port'],
        debug=config['debug']
    )
