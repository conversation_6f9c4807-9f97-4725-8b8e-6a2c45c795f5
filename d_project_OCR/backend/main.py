import os
import time
import uuid
import async<PERSON>
from typing import Optional
from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
import torch
from PIL import Image
from transformers import <PERSON><PERSON>oken<PERSON>, AutoProcessor, AutoModelForImageTextToText
import fitz  # PyMuPDF
import json
import shutil
from pathlib import Path

# Import the OCR engine from the parent directory
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main import DocumentOCR

app = FastAPI(title="D_OCR API", description="Modern OCR API with real-time processing")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("temp_images", exist_ok=True)

# Global OCR engine instance
ocr_engine: Optional[DocumentOCR] = None

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]

    async def send_message(self, client_id: str, message: dict):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except:
                self.disconnect(client_id)

manager = ConnectionManager()

@app.on_event("startup")
async def startup_event():
    """Initialize OCR engine on startup"""
    global ocr_engine
    try:
        model_path = "./model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
        if os.path.exists(model_path):
            ocr_engine = DocumentOCR(model_path)
            print("✅ OCR engine initialized successfully")
        else:
            print("❌ Model path not found, OCR engine not initialized")
    except Exception as e:
        print(f"❌ Failed to initialize OCR engine: {e}")

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for connection testing
            await manager.send_message(client_id, {"type": "ping", "data": "pong"})
    except WebSocketDisconnect:
        manager.disconnect(client_id)

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload and validate file"""
    try:
        # Validate file type
        allowed_extensions = {'.png', '.jpg', '.jpeg', '.pdf', '.bmp', '.tiff'}
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = f"{file_id}{file_extension}"
        file_path = f"uploads/{filename}"
        
        # Save file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Get file info
        file_size = os.path.getsize(file_path)
        
        # For images, get dimensions
        image_info = None
        if file_extension in {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}:
            try:
                with Image.open(file_path) as img:
                    image_info = {
                        "width": img.width,
                        "height": img.height,
                        "format": img.format
                    }
            except Exception as e:
                print(f"Warning: Could not get image info: {e}")
        
        return JSONResponse({
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "file_path": file_path,
            "file_size": file_size,
            "file_type": file_extension,
            "image_info": image_info
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.post("/process/{file_id}")
async def process_file(file_id: str, client_id: Optional[str] = None):
    """Process uploaded file with OCR"""
    try:
        if not ocr_engine:
            raise HTTPException(status_code=500, detail="OCR engine not initialized")
        
        # Find the uploaded file
        file_path = None
        for ext in ['.png', '.jpg', '.jpeg', '.pdf', '.bmp', '.tiff']:
            potential_path = f"uploads/{file_id}{ext}"
            if os.path.exists(potential_path):
                file_path = potential_path
                break
        
        if not file_path:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Send processing start message
        if client_id:
            await manager.send_message(client_id, {
                "type": "processing_start",
                "file_id": file_id
            })
        
        start_time = time.time()
        
        # Process with progress updates
        if file_path.lower().endswith('.pdf'):
            # PDF processing with page-by-page updates
            if client_id:
                await manager.send_message(client_id, {
                    "type": "progress",
                    "message": "Converting PDF to images...",
                    "progress": 10
                })
            
            # Convert PDF to images
            image_paths = ocr_engine.pdf_to_images(file_path)
            total_pages = len(image_paths)
            
            if client_id:
                await manager.send_message(client_id, {
                    "type": "progress",
                    "message": f"Processing {total_pages} pages...",
                    "progress": 20
                })
            
            all_text = ""
            for idx, img_path in enumerate(image_paths):
                if client_id:
                    progress = 20 + (idx / total_pages) * 70
                    await manager.send_message(client_id, {
                        "type": "progress",
                        "message": f"Processing page {idx + 1} of {total_pages}...",
                        "progress": int(progress)
                    })
                
                img = Image.open(img_path).convert("RGB")
                text = ocr_engine.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
                
                # Small delay to allow UI updates
                await asyncio.sleep(0.1)
        else:
            # Single image processing
            if client_id:
                await manager.send_message(client_id, {
                    "type": "progress",
                    "message": "Loading image...",
                    "progress": 30
                })
            
            img = Image.open(file_path).convert("RGB")
            
            if client_id:
                await manager.send_message(client_id, {
                    "type": "progress",
                    "message": "Running OCR analysis...",
                    "progress": 60
                })
            
            all_text = ocr_engine.extract_document_text_smart(img, page_num=0)
        
        processing_time = time.time() - start_time
        
        # Send completion message
        if client_id:
            await manager.send_message(client_id, {
                "type": "processing_complete",
                "file_id": file_id,
                "processing_time": processing_time
            })
        
        return JSONResponse({
            "success": True,
            "file_id": file_id,
            "text": all_text,
            "processing_time": processing_time,
            "character_count": len(all_text),
            "word_count": len(all_text.split())
        })
        
    except Exception as e:
        if client_id:
            await manager.send_message(client_id, {
                "type": "error",
                "message": str(e)
            })
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "ocr_engine_ready": ocr_engine is not None,
        "timestamp": time.time()
    }

@app.delete("/file/{file_id}")
async def delete_file(file_id: str):
    """Delete uploaded file"""
    try:
        deleted = False
        for ext in ['.png', '.jpg', '.jpeg', '.pdf', '.bmp', '.tiff']:
            file_path = f"uploads/{file_id}{ext}"
            if os.path.exists(file_path):
                os.remove(file_path)
                deleted = True
                break
        
        if deleted:
            return {"success": True, "message": "File deleted"}
        else:
            raise HTTPException(status_code=404, detail="File not found")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deletion failed: {str(e)}")

# Serve uploaded files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Serve static files (for the frontend)
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
