from flask import Flask, request, jsonify, render_template, send_from_directory
import os
import json
import uuid
import time

app = Flask(__name__)

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

app.config['UPLOAD_FOLDER'] = config['upload_folder']
app.config['MAX_CONTENT_LENGTH'] = config['max_file_size']

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'ocr_model_loaded': True,
        'demo_mode': True
    })

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = str(uuid.uuid4()) + '.' + file.filename.rsplit('.', 1)[1].lower()
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'url': f'/uploads/{filename}'
        })
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/process', methods=['POST'])
def process_image():
    data = request.get_json()
    filename = data.get('filename')
    
    if not filename:
        return jsonify({'error': 'No filename provided'}), 400
    
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    
    if not os.path.exists(filepath):
        return jsonify({'error': 'File not found'}), 404
    
    try:
        # Simulate processing time (reduced for faster demo)
        time.sleep(1)
        
        # Demo OCR result
        demo_text = """DEMO OCR RESULT

This is a demonstration of the OCR web interface.
In the actual implementation, this would contain
the extracted text from your uploaded image.

Features demonstrated:
• Image upload with drag & drop
• Image preview with zoom controls
• Processing animation
• Results display with copy functionality
• Clean, responsive UI design

To use the real OCR functionality:
1. Ensure your model is properly loaded
2. Run app.py instead of app_demo.py
3. Upload an image and click "Process Image"

The interface supports various image formats:
PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP

Maximum file size: 16MB"""
        
        return jsonify({
            'success': True,
            'text': demo_text
        })
    
    except Exception as e:
        return jsonify({'error': f'Processing failed: {str(e)}'}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in config['allowed_extensions']

if __name__ == '__main__':
    print("🚀 Starting OCR Web Application (DEMO MODE)...")
    print(f"📍 Server will run at: {config['app_url']}")
    print("🎭 Running in demo mode - OCR results will be simulated")
    print("🛑 Press Ctrl+C to stop the server")
    
    app.run(
        host=config['host'],
        port=config['port'],
        debug=config['debug'],
        threaded=True
    )
