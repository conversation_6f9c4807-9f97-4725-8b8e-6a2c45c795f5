{"name": "d-ocr-frontend", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^5.3.3", "web-vitals": "^3.5.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "react-dropzone": "^14.2.3", "react-zoom-pan-pinch": "^3.4.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "axios": "^1.6.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.8", "@types/testing-library__jest-dom": "^6.0.0"}, "proxy": "http://localhost:8000"}