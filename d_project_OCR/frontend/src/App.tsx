import React, { useState, useCallback, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import Header from './components/Header';
import FileUpload from './components/FileUpload';
import ImageViewer from './components/ImageViewer';
import ResultPanel from './components/ResultPanel';
import ProcessingOverlay from './components/ProcessingOverlay';
import { useWebSocket } from './hooks/useWebSocket';
import { uploadFile, processFile } from './services/api';
import toast from 'react-hot-toast';

export interface FileInfo {
  file_id: string;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  image_info?: {
    width: number;
    height: number;
    format: string;
  };
}

export interface ProcessingResult {
  success: boolean;
  file_id: string;
  text: string;
  processing_time: number;
  character_count: number;
  word_count: number;
}

export interface ProcessingProgress {
  type: 'progress' | 'processing_start' | 'processing_complete' | 'error';
  message?: string;
  progress?: number;
  file_id?: string;
  processing_time?: number;
}

function App() {
  const [fileInfo, setFileInfo] = useState<FileInfo | null>(null);
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // WebSocket connection for real-time updates
  const { sendMessage, lastMessage, connectionStatus } = useWebSocket();

  // Handle WebSocket messages
  useEffect(() => {
    if (lastMessage) {
      const data = lastMessage as ProcessingProgress;
      
      switch (data.type) {
        case 'processing_start':
          setIsProcessing(true);
          setProgress(0);
          setProgressMessage('Starting OCR processing...');
          break;
          
        case 'progress':
          if (data.progress !== undefined) {
            setProgress(data.progress);
          }
          if (data.message) {
            setProgressMessage(data.message);
          }
          break;
          
        case 'processing_complete':
          setProgress(100);
          setProgressMessage('Processing complete!');
          setTimeout(() => {
            setIsProcessing(false);
          }, 1000);
          break;
          
        case 'error':
          setIsProcessing(false);
          toast.error(data.message || 'Processing failed');
          break;
      }
    }
  }, [lastMessage]);

  const handleFileSelect = useCallback(async (file: File) => {
    try {
      setSelectedFile(file);
      setResult(null);
      
      toast.loading('Uploading file...', { id: 'upload' });
      
      const uploadResult = await uploadFile(file);
      
      if (uploadResult.success) {
        setFileInfo(uploadResult);
        toast.success('File uploaded successfully!', { id: 'upload' });
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload file', { id: 'upload' });
      setSelectedFile(null);
      setFileInfo(null);
    }
  }, []);

  const handleProcessFile = useCallback(async () => {
    if (!fileInfo) return;

    try {
      setIsProcessing(true);
      setProgress(0);
      setProgressMessage('Initializing OCR...');
      
      const result = await processFile(fileInfo.file_id);
      
      if (result.success) {
        setResult(result);
        toast.success(`OCR completed in ${result.processing_time.toFixed(2)}s`);
      } else {
        throw new Error('Processing failed');
      }
    } catch (error) {
      console.error('Processing error:', error);
      toast.error('Failed to process file');
      setIsProcessing(false);
    }
  }, [fileInfo]);

  const handleClearAll = useCallback(() => {
    setFileInfo(null);
    setResult(null);
    setSelectedFile(null);
    setIsProcessing(false);
    setProgress(0);
    setProgressMessage('');
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-12rem)]">
          {/* Left Panel - File Upload & Image Viewer */}
          <motion.div 
            className="card p-6 flex flex-col"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-800">
                Document Upload & Preview
              </h2>
              {fileInfo && (
                <button
                  onClick={handleClearAll}
                  className="btn-secondary text-sm"
                >
                  Clear All
                </button>
              )}
            </div>
            
            <div className="flex-1 flex flex-col">
              {!fileInfo ? (
                <FileUpload onFileSelect={handleFileSelect} />
              ) : (
                <div className="flex-1 flex flex-col">
                  {/* File Info */}
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-800">{fileInfo.filename}</p>
                        <p className="text-sm text-gray-600">
                          {(fileInfo.file_size / 1024).toFixed(1)} KB
                          {fileInfo.image_info && (
                            <span className="ml-2">
                              • {fileInfo.image_info.width} × {fileInfo.image_info.height}
                            </span>
                          )}
                        </p>
                      </div>
                      <button
                        onClick={handleProcessFile}
                        disabled={isProcessing}
                        className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? 'Processing...' : 'Start OCR'}
                      </button>
                    </div>
                  </div>
                  
                  {/* Image Viewer */}
                  <div className="flex-1">
                    <ImageViewer 
                      fileInfo={fileInfo}
                      isProcessing={isProcessing}
                    />
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Right Panel - Results */}
          <motion.div 
            className="card p-6 flex flex-col"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-800">
                OCR Results
              </h2>
              {result && (
                <div className="text-sm text-gray-600">
                  {result.character_count} chars • {result.word_count} words
                </div>
              )}
            </div>
            
            <div className="flex-1">
              <ResultPanel result={result} />
            </div>
          </motion.div>
        </div>
      </main>

      {/* Processing Overlay */}
      <AnimatePresence>
        {isProcessing && (
          <ProcessingOverlay
            progress={progress}
            message={progressMessage}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;
