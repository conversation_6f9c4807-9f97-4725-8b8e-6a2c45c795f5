import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 300000, // 5 minutes timeout for large file processing
});

export interface UploadResponse {
  success: boolean;
  file_id: string;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  image_info?: {
    width: number;
    height: number;
    format: string;
  };
}

export interface ProcessResponse {
  success: boolean;
  file_id: string;
  text: string;
  processing_time: number;
  character_count: number;
  word_count: number;
}

export interface HealthResponse {
  status: string;
  ocr_engine_ready: boolean;
  timestamp: number;
}

export const uploadFile = async (file: File): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await api.post<UploadResponse>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  } catch (error) {
    console.error('Upload error:', error);
    throw new Error('Failed to upload file');
  }
};

export const processFile = async (fileId: string, clientId?: string): Promise<ProcessResponse> => {
  try {
    const params = clientId ? { client_id: clientId } : {};
    const response = await api.post<ProcessResponse>(`/process/${fileId}`, null, {
      params,
    });
    
    return response.data;
  } catch (error) {
    console.error('Processing error:', error);
    throw new Error('Failed to process file');
  }
};

export const deleteFile = async (fileId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.delete(`/file/${fileId}`);
    return response.data;
  } catch (error) {
    console.error('Delete error:', error);
    throw new Error('Failed to delete file');
  }
};

export const checkHealth = async (): Promise<HealthResponse> => {
  try {
    const response = await api.get<HealthResponse>('/health');
    return response.data;
  } catch (error) {
    console.error('Health check error:', error);
    throw new Error('Failed to check API health');
  }
};

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      console.error('API Error:', error.response.status, error.response.data);
      
      if (error.response.status === 413) {
        throw new Error('File too large. Please upload a smaller file.');
      } else if (error.response.status === 415) {
        throw new Error('Unsupported file type. Please upload PNG, JPG, JPEG, or PDF files.');
      } else if (error.response.status >= 500) {
        throw new Error('Server error. Please try again later.');
      } else {
        throw new Error(error.response.data?.detail || 'An error occurred');
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.request);
      throw new Error('Network error. Please check your connection.');
    } else {
      // Other error
      console.error('Error:', error.message);
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }
);

export default api;
