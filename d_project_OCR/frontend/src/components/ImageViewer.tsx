import React, { useState, useRef, useEffect } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { motion } from 'framer-motion';
import { 
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowsPointingOutIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { FileInfo } from '../App';

interface ImageViewerProps {
  fileInfo: FileInfo;
  isProcessing: boolean;
}

const ImageViewer: React.FC<ImageViewerProps> = ({ fileInfo, isProcessing }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const transformRef = useRef<any>(null);

  const isPDF = fileInfo.file_type === '.pdf';
  const imageUrl = isPDF ? null : `/uploads/${fileInfo.file_id}${fileInfo.file_type}`;

  useEffect(() => {
    setImageLoaded(false);
    setImageError(false);
  }, [fileInfo]);

  const handleZoomIn = () => {
    if (transformRef.current) {
      transformRef.current.zoomIn(0.5);
    }
  };

  const handleZoomOut = () => {
    if (transformRef.current) {
      transformRef.current.zoomOut(0.5);
    }
  };

  const handleResetTransform = () => {
    if (transformRef.current) {
      transformRef.current.resetTransform();
    }
  };

  if (isPDF) {
    return (
      <div className="image-viewer">
        <div className="image-container">
          <motion.div
            className="flex flex-col items-center justify-center space-y-4 text-gray-500"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <DocumentIcon className="h-16 w-16" />
            <div className="text-center">
              <h3 className="font-medium text-gray-700">PDF Document</h3>
              <p className="text-sm text-gray-500 mt-1">
                {fileInfo.filename}
              </p>
              <p className="text-xs text-gray-400 mt-2">
                PDF preview not available. Click "Start OCR" to process.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="image-viewer relative">
      <TransformWrapper
        ref={transformRef}
        initialScale={1}
        minScale={0.1}
        maxScale={5}
        centerOnInit={true}
        wheel={{ step: 0.1 }}
        pinch={{ step: 5 }}
        doubleClick={{ mode: 'reset' }}
      >
        <TransformComponent
          wrapperClass="w-full h-full"
          contentClass="w-full h-full flex items-center justify-center"
        >
          <div className="relative">
            {!imageLoaded && !imageError && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="shimmer w-64 h-64 rounded-lg"></div>
              </div>
            )}
            
            {imageError ? (
              <motion.div
                className="flex flex-col items-center justify-center space-y-4 text-gray-500 p-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <DocumentIcon className="h-16 w-16" />
                <div className="text-center">
                  <h3 className="font-medium text-gray-700">Failed to load image</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Please try uploading the file again
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.img
                src={imageUrl || ''}
                alt={fileInfo.filename}
                className={`max-w-none transition-opacity duration-300 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                style={{
                  maxHeight: '80vh',
                  maxWidth: '80vw',
                }}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ 
                  opacity: imageLoaded ? 1 : 0, 
                  scale: imageLoaded ? 1 : 0.9 
                }}
                transition={{ duration: 0.3 }}
              />
            )}
            
            {/* Processing overlay */}
            {isProcessing && imageLoaded && (
              <motion.div
                className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="bg-white rounded-lg p-4 shadow-lg">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    <span className="text-sm font-medium text-gray-700">
                      Processing image...
                    </span>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </TransformComponent>
      </TransformWrapper>

      {/* Zoom Controls */}
      {imageLoaded && !imageError && (
        <motion.div
          className="zoom-controls"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <button
            onClick={handleZoomIn}
            className="zoom-btn"
            title="Zoom In"
          >
            <MagnifyingGlassPlusIcon className="h-5 w-5 text-gray-600" />
          </button>
          
          <button
            onClick={handleZoomOut}
            className="zoom-btn"
            title="Zoom Out"
          >
            <MagnifyingGlassMinusIcon className="h-5 w-5 text-gray-600" />
          </button>
          
          <button
            onClick={handleResetTransform}
            className="zoom-btn"
            title="Reset Zoom"
          >
            <ArrowsPointingOutIcon className="h-5 w-5 text-gray-600" />
          </button>
        </motion.div>
      )}

      {/* Image Info */}
      {fileInfo.image_info && imageLoaded && (
        <motion.div
          className="absolute top-4 left-4 bg-black bg-opacity-60 text-white px-3 py-2 rounded-lg text-sm"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {fileInfo.image_info.width} × {fileInfo.image_info.height} px
        </motion.div>
      )}
    </div>
  );
};

export default ImageViewer;
