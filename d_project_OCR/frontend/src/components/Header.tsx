import React from 'react';
import { motion } from 'framer-motion';
import { DocumentTextIcon, SparklesIcon } from '@heroicons/react/24/outline';

const Header: React.FC = () => {
  return (
    <motion.header 
      className="bg-white shadow-sm border-b border-gray-200"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <DocumentTextIcon className="h-8 w-8 text-primary-600" />
              <SparklesIcon className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gradient">
                D_OCR
              </h1>
              <p className="text-sm text-gray-600">
                Advanced Document OCR with AI
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>AI Powered</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Real-time Processing</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Multi-format Support</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 px-3 py-1 bg-primary-50 rounded-full">
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-primary-700">
                Ready
              </span>
            </div>
          </div>
        </div>
        
        {/* Feature highlights */}
        <div className="mt-4 flex flex-wrap gap-2">
          {[
            'PDF & Image Support',
            'High Accuracy OCR',
            'Real-time Progress',
            'Copy to Clipboard',
            'Zoom & Pan',
            'Drag & Drop'
          ].map((feature, index) => (
            <motion.span
              key={feature}
              className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {feature}
            </motion.span>
          ))}
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
