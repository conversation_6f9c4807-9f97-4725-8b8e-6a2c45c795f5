import React from 'react';
import { motion } from 'framer-motion';
import { CogIcon, SparklesIcon } from '@heroicons/react/24/outline';

interface ProcessingOverlayProps {
  progress: number;
  message: string;
}

const ProcessingOverlay: React.FC<ProcessingOverlayProps> = ({ progress, message }) => {
  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="bg-white rounded-2xl p-8 shadow-2xl max-w-md w-full mx-4"
        initial={{ scale: 0.8, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.8, y: 20 }}
        transition={{ duration: 0.3 }}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className="relative inline-block">
            <motion.div
              className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            >
              <CogIcon className="h-8 w-8 text-primary-600" />
            </motion.div>
            
            <motion.div
              className="absolute -top-1 -right-1"
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360] 
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity, 
                ease: "easeInOut" 
              }}
            >
              <SparklesIcon className="h-6 w-6 text-yellow-500" />
            </motion.div>
          </div>
          
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            Processing Document
          </h3>
          <p className="text-gray-600">
            AI is analyzing your document...
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm font-medium text-primary-600">
              {Math.round(progress)}%
            </span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Status Message */}
        <motion.div
          className="text-center"
          key={message}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <p className="text-sm text-gray-600 mb-4">
            {message}
          </p>
          
          {/* Processing steps indicator */}
          <div className="flex justify-center space-x-2">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="w-2 h-2 bg-primary-400 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        </motion.div>

        {/* Fun facts while processing */}
        <motion.div
          className="mt-6 p-4 bg-blue-50 rounded-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          <p className="text-xs text-blue-700 text-center">
            💡 <strong>Did you know?</strong> Our AI can recognize text in over 100 languages 
            and handle complex layouts with high accuracy!
          </p>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default ProcessingOverlay;
