import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { 
  CloudArrowUpIcon, 
  DocumentIcon, 
  PhotoIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileSelect }) => {
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setDragActive(false);
    
    if (rejectedFiles.length > 0) {
      // Handle rejected files
      const rejection = rejectedFiles[0];
      if (rejection.errors[0]?.code === 'file-too-large') {
        alert('File is too large. Maximum size is 50MB.');
      } else if (rejection.errors[0]?.code === 'file-invalid-type') {
        alert('Invalid file type. Please upload PNG, JPG, JPEG, or PDF files.');
      }
      return;
    }

    if (acceptedFiles.length > 0) {
      onFileSelect(acceptedFiles[0]);
    }
  }, [onFileSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.bmp', '.tiff'],
      'application/pdf': ['.pdf']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: false
  });

  const supportedFormats = [
    { name: 'Images', types: 'PNG, JPG, JPEG, BMP, TIFF', icon: PhotoIcon },
    { name: 'Documents', types: 'PDF', icon: DocumentIcon }
  ];

  return (
    <div className="h-full flex flex-col">
      <motion.div
        {...getRootProps()}
        className={`
          flex-1 upload-area cursor-pointer
          ${isDragActive || dragActive ? 'dragover' : ''}
        `}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center justify-center h-full space-y-6">
          <motion.div
            className="relative"
            animate={{ 
              y: isDragActive ? -10 : 0,
              scale: isDragActive ? 1.1 : 1 
            }}
            transition={{ duration: 0.2 }}
          >
            <CloudArrowUpIcon 
              className={`
                h-16 w-16 mx-auto transition-colors duration-200
                ${isDragActive ? 'text-primary-500' : 'text-gray-400'}
              `} 
            />
            {isDragActive && (
              <motion.div
                className="absolute inset-0 bg-primary-500 rounded-full opacity-20"
                initial={{ scale: 0 }}
                animate={{ scale: 1.5 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </motion.div>
          
          <div className="text-center">
            <h3 className={`
              text-lg font-semibold transition-colors duration-200
              ${isDragActive ? 'text-primary-700' : 'text-gray-700'}
            `}>
              {isDragActive ? 'Drop your file here' : 'Upload your document'}
            </h3>
            <p className="text-gray-500 mt-2">
              Drag and drop your file here, or{' '}
              <span className="text-primary-600 font-medium">browse</span>
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-md">
            {supportedFormats.map((format, index) => (
              <motion.div
                key={format.name}
                className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <format.icon className="h-6 w-6 text-gray-600" />
                <div>
                  <p className="font-medium text-gray-800 text-sm">{format.name}</p>
                  <p className="text-xs text-gray-600">{format.types}</p>
                </div>
              </motion.div>
            ))}
          </div>
          
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <span>Maximum file size: 50MB</span>
          </div>
        </div>
      </motion.div>
      
      {/* Quick tips */}
      <motion.div 
        className="mt-6 p-4 bg-blue-50 rounded-lg"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h4 className="font-medium text-blue-800 mb-2">💡 Tips for best results:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Ensure text is clear and well-lit</li>
          <li>• Avoid blurry or skewed images</li>
          <li>• Higher resolution images work better</li>
          <li>• PDFs are automatically converted to images</li>
        </ul>
      </motion.div>
    </div>
  );
};

export default FileUpload;
