import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ClipboardDocumentIcon,
  CheckIcon,
  DocumentTextIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { ProcessingResult } from '../App';
import toast from 'react-hot-toast';

interface ResultPanelProps {
  result: ProcessingResult | null;
}

const ResultPanel: React.FC<ResultPanelProps> = ({ result }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    if (!result?.text) return;

    try {
      await navigator.clipboard.writeText(result.text);
      setCopied(true);
      toast.success('Text copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy text');
    }
  };

  if (!result) {
    return (
      <div className="result-panel">
        <div className="flex-1 flex items-center justify-center">
          <motion.div
            className="text-center text-gray-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <DocumentTextIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No results yet
            </h3>
            <p className="text-sm text-gray-500">
              Upload a document and start OCR processing to see results here
            </p>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className="result-panel"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Stats Bar */}
      <motion.div
        className="mb-4 grid grid-cols-3 gap-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="bg-blue-50 rounded-lg p-3 text-center">
          <ChartBarIcon className="h-5 w-5 text-blue-600 mx-auto mb-1" />
          <p className="text-xs text-blue-600 font-medium">Characters</p>
          <p className="text-lg font-bold text-blue-800">
            {result.character_count.toLocaleString()}
          </p>
        </div>
        
        <div className="bg-green-50 rounded-lg p-3 text-center">
          <DocumentTextIcon className="h-5 w-5 text-green-600 mx-auto mb-1" />
          <p className="text-xs text-green-600 font-medium">Words</p>
          <p className="text-lg font-bold text-green-800">
            {result.word_count.toLocaleString()}
          </p>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-3 text-center">
          <ClockIcon className="h-5 w-5 text-purple-600 mx-auto mb-1" />
          <p className="text-xs text-purple-600 font-medium">Time</p>
          <p className="text-lg font-bold text-purple-800">
            {result.processing_time.toFixed(1)}s
          </p>
        </div>
      </motion.div>

      {/* Copy Button */}
      <motion.div
        className="mb-4"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <button
          onClick={handleCopy}
          className={`
            w-full flex items-center justify-center space-x-2 py-2 px-4 rounded-lg
            transition-all duration-200 font-medium
            ${copied 
              ? 'bg-green-100 text-green-700 border border-green-200' 
              : 'bg-primary-600 hover:bg-primary-700 text-white'
            }
          `}
        >
          <AnimatePresence mode="wait">
            {copied ? (
              <motion.div
                key="check"
                className="flex items-center space-x-2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.2 }}
              >
                <CheckIcon className="h-4 w-4" />
                <span>Copied!</span>
              </motion.div>
            ) : (
              <motion.div
                key="copy"
                className="flex items-center space-x-2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                transition={{ duration: 0.2 }}
              >
                <ClipboardDocumentIcon className="h-4 w-4" />
                <span>Copy to Clipboard</span>
              </motion.div>
            )}
          </AnimatePresence>
        </button>
      </motion.div>

      {/* Text Content */}
      <motion.div
        className="result-content"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <div className="bg-gray-50 rounded-lg p-4 h-full">
          <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed custom-scrollbar overflow-auto h-full">
            {result.text || 'No text extracted'}
          </pre>
        </div>
      </motion.div>

      {/* Success indicator */}
      {result.success && (
        <motion.div
          className="mt-4 flex items-center justify-center space-x-2 text-green-600"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <CheckIcon className="h-5 w-5" />
          <span className="text-sm font-medium">
            OCR processing completed successfully
          </span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default ResultPanel;
