@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

/* Animation keyframes */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Processing animation */
@keyframes processing {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.processing-animation {
  animation: processing 2s ease-in-out infinite;
}

/* Zoom controls */
.zoom-controls {
  @apply fixed bottom-6 right-6 flex flex-col gap-2 z-50;
}

.zoom-btn {
  @apply w-12 h-12 bg-white shadow-lg rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors duration-200 border border-gray-200;
}

/* File upload area */
.upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-xl p-8 text-center transition-colors duration-200;
}

.upload-area.dragover {
  @apply border-primary-500 bg-primary-50;
}

/* Result panel */
.result-panel {
  @apply h-full flex flex-col;
}

.result-content {
  @apply flex-1 overflow-auto custom-scrollbar;
}

/* Image viewer */
.image-viewer {
  @apply relative w-full h-full overflow-hidden bg-gray-100 rounded-lg;
}

.image-container {
  @apply w-full h-full flex items-center justify-center;
}

/* Progress bar */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-primary-500 to-primary-600 transition-all duration-300 ease-out;
}

/* Toast notifications */
.toast-success {
  @apply bg-green-500 text-white;
}

.toast-error {
  @apply bg-red-500 text-white;
}

.toast-info {
  @apply bg-blue-500 text-white;
}
