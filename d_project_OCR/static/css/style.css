/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #4f46e5;
}

.logo i {
    font-size: 2rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s;
}

.nav a:hover {
    color: #4f46e5;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* OCR Interface */
.ocr-interface {
    padding: 4rem 0;
    position: relative;
}

.interface-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

/* Upload Section */
.upload-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: #4f46e5;
    background: #f8fafc;
}

.upload-area.dragover {
    border-color: #4f46e5;
    background: #eef2ff;
}

.upload-content i {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.upload-content h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #374151;
}

.upload-content p {
    color: #6b7280;
    margin-bottom: 1rem;
}

.supported-formats {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* File Info */
.file-info {
    background: #f3f4f6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-details i {
    font-size: 1.5rem;
    color: #4f46e5;
}

.file-meta {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: 600;
    color: #374151;
}

.file-size {
    font-size: 0.875rem;
    color: #6b7280;
}

.btn-remove {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s;
}

.btn-remove:hover {
    background: #fee2e2;
}

/* Buttons */
.btn-process {
    width: 100%;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-process:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.btn-process:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

/* Preview Section */
.preview-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.preview-header h3 {
    font-size: 1.25rem;
    color: #374151;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoom-btn {
    background: #f3f4f6;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.zoom-btn:hover {
    background: #e5e7eb;
}

.zoom-level {
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

.preview-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    height: 400px;
    overflow: hidden;
    position: relative;
    background: #f9fafb;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
}

.preview-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

#previewImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s;
    cursor: grab;
}

#previewImage:active {
    cursor: grabbing;
}

/* Processing Overlay */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.processing-content {
    background: white;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    max-width: 500px;
    width: 90%;
}

.processing-animation {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

.processing-animation i {
    font-size: 2.5rem;
    color: white;
}

.scanner-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: #10b981;
    animation: scan 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes scan {
    0% { transform: translateY(0) scaleX(0); }
    50% { transform: translateY(50px) scaleX(1); }
    100% { transform: translateY(100px) scaleX(0); }
}

.processing-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #374151;
}

.processing-content p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 2rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    border-radius: 3px;
    transition: width 0.3s;
    animation: progress 3s ease-in-out;
}

@keyframes progress {
    0% { width: 0%; }
    25% { width: 25%; }
    50% { width: 50%; }
    75% { width: 75%; }
    100% { width: 100%; }
}

.processing-steps {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.3;
    transition: opacity 0.3s;
}

.step.active {
    opacity: 1;
}

.step i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.step.active i {
    background: #4f46e5;
    color: white;
}

.step span {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.step.active span {
    color: #374151;
}

/* Results Section */
.results-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.results-header h3 {
    font-size: 1.25rem;
    color: #374151;
}

.results-actions {
    display: flex;
    gap: 1rem;
}

.results-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.stat-value {
    font-weight: 600;
    color: #374151;
}

.text-output {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

#extractedText {
    width: 100%;
    min-height: 300px;
    padding: 1.5rem;
    border: none;
    resize: vertical;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    background: #fafafa;
}

#extractedText:focus {
    outline: none;
    background: white;
}

/* Features Section */
.features {
    padding: 4rem 0;
    background: white;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #374151;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: #f9fafb;
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-card i {
    font-size: 3rem;
    color: #4f46e5;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #374151;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #f9fafb;
}

.footer-section a {
    display: block;
    color: #d1d5db;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #4f46e5;
}

.footer-section .logo {
    color: #4f46e5;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #9ca3af;
    line-height: 1.6;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .interface-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .results-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .results-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .nav {
        display: none;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Success Animation */
@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.success-checkmark {
    animation: checkmark 0.5s ease-out;
}
