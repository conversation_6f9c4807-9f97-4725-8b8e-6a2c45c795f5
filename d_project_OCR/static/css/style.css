* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance optimizations */
html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    will-change: auto;
    transform: translateZ(0);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 300;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    flex: 1;
    min-height: 600px;
}

.left-panel, .right-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Upload Section */
.upload-section {
    padding: 30px;
}

.upload-area {
    border: 3px dashed #ddd;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e8f2ff;
    transform: scale(1.02);
}

.upload-content .upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-content p {
    font-size: 1.1rem;
    margin-bottom: 8px;
    color: #555;
}

.upload-hint {
    font-size: 0.9rem !important;
    color: #888 !important;
}

/* Image Preview */
.image-preview {
    padding: 20px;
    overflow: hidden; /* Prevent any unwanted scrollbars */
}

.image-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.control-btn {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: #e9e9e9;
    border-color: #ccc;
}

.process-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-left: auto;
    transition: all 0.2s ease;
}

.process-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.process-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.image-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    max-height: 500px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Prevent multiple scrollbars */
    box-sizing: border-box;
}

.image-container img {
    max-width: none;
    max-height: none;
    width: auto;
    height: auto;
    display: block;
    transition: transform 0.15s ease;
    transform-origin: center center;
    /* Ensure image fits container initially */
    max-width: 100%;
    max-height: 100%;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f0f0f0;
}

/* Results Section */
.results-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.results-header {
    padding: 20px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h3 {
    font-size: 1.3rem;
    color: #333;
    font-weight: 500;
}

.copy-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.copy-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.results-content {
    flex: 1;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder {
    text-align: center;
    color: #888;
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.placeholder p {
    font-size: 1.1rem;
}

/* Loading Animation */
.loading {
    text-align: center;
    color: #667eea;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Results Text */
.results-text {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.results-text pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #333;
    max-height: 500px;
    overflow-y: auto;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #28a745;
}

.toast.error {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .upload-area {
        padding: 40px 15px;
    }
    
    .image-controls {
        flex-wrap: wrap;
    }
    
    .process-btn {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
}
