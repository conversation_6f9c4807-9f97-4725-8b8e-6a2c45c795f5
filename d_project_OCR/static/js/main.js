class OCRApp {
    constructor() {
        this.currentFile = null;
        this.currentZoom = 1;
        this.initializeElements();
        this.bindEvents();
        this.loadConfig();
    }

    async loadConfig() {
        try {
            const response = await fetch('/static/config.json');
            this.config = await response.json();
        } catch (error) {
            console.error('Failed to load config:', error);
            this.config = { app_url: window.location.origin };
        }
    }

    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.imagePreview = document.getElementById('imagePreview');
        this.previewImage = document.getElementById('previewImage');
        this.processBtn = document.getElementById('processBtn');
        this.zoomInBtn = document.getElementById('zoomIn');
        this.zoomOutBtn = document.getElementById('zoomOut');
        this.resetZoomBtn = document.getElementById('resetZoom');
        this.placeholder = document.getElementById('placeholder');
        this.loading = document.getElementById('loading');
        this.resultsText = document.getElementById('resultsText');
        this.ocrOutput = document.getElementById('ocrOutput');
        this.copyBtn = document.getElementById('copyBtn');
        this.toast = document.getElementById('toast');
    }

    bindEvents() {
        // File upload events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // Drag and drop events
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Image controls
        this.zoomInBtn.addEventListener('click', () => this.zoomIn());
        this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
        this.resetZoomBtn.addEventListener('click', () => this.resetZoom());
        this.processBtn.addEventListener('click', () => this.processImage());
        
        // Copy button
        this.copyBtn.addEventListener('click', () => this.copyResults());
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelect(files[0]);
        }
    }

    async handleFileSelect(file) {
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            this.showToast('Please select a valid image file', 'error');
            return;
        }

        // Validate file size (16MB max)
        if (file.size > 16 * 1024 * 1024) {
            this.showToast('File size must be less than 16MB', 'error');
            return;
        }

        try {
            // Upload file
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentFile = result.filename;
                this.displayImage(result.url);
                this.showToast('Image uploaded successfully', 'success');
            } else {
                this.showToast(result.error || 'Upload failed', 'error');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showToast('Upload failed', 'error');
        }
    }

    displayImage(imageUrl) {
        this.previewImage.src = imageUrl;
        this.imagePreview.style.display = 'block';
        this.resetZoom();
        this.hideResults();
    }

    zoomIn() {
        this.currentZoom = Math.min(this.currentZoom * 1.2, 5);
        this.applyZoom();
    }

    zoomOut() {
        this.currentZoom = Math.max(this.currentZoom / 1.2, 0.1);
        this.applyZoom();
    }

    resetZoom() {
        this.currentZoom = 1;
        this.applyZoom();
    }

    applyZoom() {
        this.previewImage.style.transform = `scale(${this.currentZoom})`;
    }

    async processImage() {
        if (!this.currentFile) {
            this.showToast('Please upload an image first', 'error');
            return;
        }

        this.showLoading();
        this.processBtn.disabled = true;

        try {
            const response = await fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: this.currentFile
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showResults(result.text);
                this.showToast('OCR processing completed', 'success');
            } else {
                this.showToast(result.error || 'Processing failed', 'error');
                this.hideResults();
            }
        } catch (error) {
            console.error('Processing error:', error);
            this.showToast('Processing failed', 'error');
            this.hideResults();
        } finally {
            this.hideLoading();
            this.processBtn.disabled = false;
        }
    }

    showLoading() {
        this.placeholder.style.display = 'none';
        this.resultsText.style.display = 'none';
        this.loading.style.display = 'flex';
        this.copyBtn.style.display = 'none';
    }

    hideLoading() {
        this.loading.style.display = 'none';
    }

    showResults(text) {
        this.placeholder.style.display = 'none';
        this.loading.style.display = 'none';
        this.resultsText.style.display = 'block';
        this.ocrOutput.textContent = text;
        this.copyBtn.style.display = 'block';
    }

    hideResults() {
        this.resultsText.style.display = 'none';
        this.copyBtn.style.display = 'none';
        this.placeholder.style.display = 'block';
    }

    async copyResults() {
        try {
            await navigator.clipboard.writeText(this.ocrOutput.textContent);
            this.showToast('Results copied to clipboard', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            this.showToast('Failed to copy results', 'error');
        }
    }

    showToast(message, type = 'info') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');

        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new OCRApp();
});
