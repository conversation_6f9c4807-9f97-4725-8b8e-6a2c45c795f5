class OCRApp {
    constructor() {
        this.currentFile = null;
        this.currentZoom = 1;
        this.minZoom = 0.1;
        this.maxZoom = 5;
        this.originalImageSize = { width: 0, height: 0 };
        this.initializeElements();
        this.bindEvents();
        // Remove async config loading to speed up initialization
        this.config = { app_url: window.location.origin };
    }

    initializeElements() {
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.imagePreview = document.getElementById('imagePreview');
        this.previewImage = document.getElementById('previewImage');
        this.processBtn = document.getElementById('processBtn');
        this.zoomInBtn = document.getElementById('zoomIn');
        this.zoomOutBtn = document.getElementById('zoomOut');
        this.resetZoomBtn = document.getElementById('resetZoom');
        this.placeholder = document.getElementById('placeholder');
        this.loading = document.getElementById('loading');
        this.resultsText = document.getElementById('resultsText');
        this.ocrOutput = document.getElementById('ocrOutput');
        this.copyBtn = document.getElementById('copyBtn');
        this.toast = document.getElementById('toast');
    }

    bindEvents() {
        // File upload events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // Drag and drop events
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Image controls
        this.zoomInBtn.addEventListener('click', () => this.zoomIn());
        this.zoomOutBtn.addEventListener('click', () => this.zoomOut());
        this.resetZoomBtn.addEventListener('click', () => this.resetZoom());
        this.processBtn.addEventListener('click', () => this.processImage());
        
        // Copy button
        this.copyBtn.addEventListener('click', () => this.copyResults());
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelect(files[0]);
        }
    }

    async handleFileSelect(file) {
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            this.showToast('Please select a valid image file', 'error');
            return;
        }

        // Validate file size (16MB max)
        if (file.size > 16 * 1024 * 1024) {
            this.showToast('File size must be less than 16MB', 'error');
            return;
        }

        try {
            // Upload file
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentFile = result.filename;
                this.displayImage(result.url);
                this.showToast('Image uploaded successfully', 'success');
            } else {
                this.showToast(result.error || 'Upload failed', 'error');
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showToast('Upload failed', 'error');
        }
    }

    displayImage(imageUrl) {
        this.previewImage.onload = () => {
            // Store original image dimensions
            this.originalImageSize.width = this.previewImage.naturalWidth;
            this.originalImageSize.height = this.previewImage.naturalHeight;
            this.resetZoom();
        };
        this.previewImage.src = imageUrl;
        this.imagePreview.style.display = 'block';
        this.hideResults();
    }

    zoomIn() {
        this.currentZoom = Math.min(this.currentZoom * 1.2, this.maxZoom);
        this.applyZoom();
    }

    zoomOut() {
        // Don't allow zoom out beyond original size (1.0)
        this.currentZoom = Math.max(this.currentZoom / 1.2, 1.0);
        this.applyZoom();
    }

    resetZoom() {
        this.currentZoom = 1;
        this.applyZoom();
    }

    applyZoom() {
        const container = this.previewImage.parentElement;
        const img = this.previewImage;

        // Apply zoom transform
        img.style.transform = `scale(${this.currentZoom})`;

        // Handle container overflow and scrolling
        if (this.currentZoom > 1) {
            container.style.overflow = 'auto';
            container.style.cursor = 'grab';

            // Calculate the actual displayed image dimensions
            const imgRect = img.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            // Ensure proper scrolling by setting container dimensions
            const scaledWidth = img.offsetWidth * this.currentZoom;
            const scaledHeight = img.offsetHeight * this.currentZoom;

            // Create a wrapper div to handle scrolling properly
            if (!container.querySelector('.zoom-wrapper')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'zoom-wrapper';
                wrapper.style.width = `${scaledWidth}px`;
                wrapper.style.height = `${scaledHeight}px`;
                wrapper.style.display = 'flex';
                wrapper.style.alignItems = 'center';
                wrapper.style.justifyContent = 'center';
                wrapper.style.minWidth = '100%';
                wrapper.style.minHeight = '100%';

                container.appendChild(wrapper);
                wrapper.appendChild(img);
            } else {
                const wrapper = container.querySelector('.zoom-wrapper');
                wrapper.style.width = `${scaledWidth}px`;
                wrapper.style.height = `${scaledHeight}px`;
            }
        } else {
            container.style.overflow = 'hidden';
            container.style.cursor = 'default';

            // Remove wrapper and reset
            const wrapper = container.querySelector('.zoom-wrapper');
            if (wrapper) {
                container.appendChild(img);
                wrapper.remove();
            }

            container.scrollLeft = 0;
            container.scrollTop = 0;
        }

        // Update zoom control states
        this.updateZoomControls();
    }

    updateZoomControls() {
        const zoomOutBtn = this.zoomOutBtn;
        const zoomInBtn = this.zoomInBtn;

        // Disable zoom out if at minimum (original size)
        zoomOutBtn.disabled = this.currentZoom <= 1.0;
        zoomOutBtn.style.opacity = this.currentZoom <= 1.0 ? '0.5' : '1';

        // Disable zoom in if at maximum
        zoomInBtn.disabled = this.currentZoom >= this.maxZoom;
        zoomInBtn.style.opacity = this.currentZoom >= this.maxZoom ? '0.5' : '1';
    }

    async processImage() {
        if (!this.currentFile) {
            this.showToast('Please upload an image first', 'error');
            return;
        }

        this.showLoading();
        this.processBtn.disabled = true;

        try {
            const response = await fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: this.currentFile
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showResults(result.text);
                this.showToast('OCR processing completed', 'success');
            } else {
                this.showToast(result.error || 'Processing failed', 'error');
                this.hideResults();
            }
        } catch (error) {
            console.error('Processing error:', error);
            this.showToast('Processing failed', 'error');
            this.hideResults();
        } finally {
            this.hideLoading();
            this.processBtn.disabled = false;
        }
    }

    showLoading() {
        this.placeholder.style.display = 'none';
        this.resultsText.style.display = 'none';
        this.loading.style.display = 'flex';
        this.copyBtn.style.display = 'none';
    }

    hideLoading() {
        this.loading.style.display = 'none';
    }

    showResults(text) {
        this.placeholder.style.display = 'none';
        this.loading.style.display = 'none';
        this.resultsText.style.display = 'block';
        this.ocrOutput.textContent = text;
        this.copyBtn.style.display = 'block';
    }

    hideResults() {
        this.resultsText.style.display = 'none';
        this.copyBtn.style.display = 'none';
        this.placeholder.style.display = 'block';
    }

    async copyResults() {
        try {
            await navigator.clipboard.writeText(this.ocrOutput.textContent);
            this.showToast('Results copied to clipboard', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            this.showToast('Failed to copy results', 'error');
        }
    }

    showToast(message, type = 'info') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');

        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize the app when DOM is loaded - optimized for fast startup
document.addEventListener('DOMContentLoaded', () => {
    new OCRApp();
});

// Preload the app if DOM is already ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new OCRApp());
} else {
    new OCRApp();
}
