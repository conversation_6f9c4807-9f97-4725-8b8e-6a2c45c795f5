class OCRApp {
    constructor() {
        this.currentFile = null;
        this.currentFileId = null;
        this.zoomLevel = 1;
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.imagePosition = { x: 0, y: 0 };
        
        this.initializeElements();
        this.bindEvents();
    }

    initializeElements() {
        // Upload elements
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.fileInfo = document.getElementById('fileInfo');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        this.removeFileBtn = document.getElementById('removeFile');
        this.processBtn = document.getElementById('processBtn');

        // Preview elements
        this.previewContainer = document.getElementById('previewContainer');
        this.previewImage = document.getElementById('previewImage');
        this.zoomControls = document.getElementById('zoomControls');
        this.zoomLevel = document.getElementById('zoomLevel');
        this.zoomInBtn = document.getElementById('zoomIn');
        this.zoomOutBtn = document.getElementById('zoomOut');
        this.resetZoomBtn = document.getElementById('resetZoom');

        // Processing elements
        this.processingOverlay = document.getElementById('processingOverlay');
        this.progressFill = document.getElementById('progressFill');
        this.step1 = document.getElementById('step1');
        this.step2 = document.getElementById('step2');
        this.step3 = document.getElementById('step3');
        this.step4 = document.getElementById('step4');

        // Results elements
        this.resultsSection = document.getElementById('resultsSection');
        this.extractedText = document.getElementById('extractedText');
        this.processingTime = document.getElementById('processingTime');
        this.wordCount = document.getElementById('wordCount');
        this.charCount = document.getElementById('charCount');
        this.copyTextBtn = document.getElementById('copyText');
        this.downloadTextBtn = document.getElementById('downloadText');
        this.newDocumentBtn = document.getElementById('newDocument');
    }

    bindEvents() {
        // Upload events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        this.removeFileBtn.addEventListener('click', this.removeFile.bind(this));
        this.processBtn.addEventListener('click', this.processFile.bind(this));

        // Zoom events
        this.zoomInBtn.addEventListener('click', () => this.adjustZoom(0.2));
        this.zoomOutBtn.addEventListener('click', () => this.adjustZoom(-0.2));
        this.resetZoomBtn.addEventListener('click', this.resetZoom.bind(this));

        // Image drag events
        this.previewImage.addEventListener('mousedown', this.startDrag.bind(this));
        document.addEventListener('mousemove', this.drag.bind(this));
        document.addEventListener('mouseup', this.endDrag.bind(this));

        // Results events
        this.copyTextBtn.addEventListener('click', this.copyText.bind(this));
        this.downloadTextBtn.addEventListener('click', this.downloadText.bind(this));
        this.newDocumentBtn.addEventListener('click', this.resetApp.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.handleFile(file);
        }
    }

    handleFile(file) {
        // Validate file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            this.showError('Please select a valid image or PDF file.');
            return;
        }

        // Validate file size (16MB max)
        if (file.size > 16 * 1024 * 1024) {
            this.showError('File size must be less than 16MB.');
            return;
        }

        this.currentFile = file;
        this.displayFileInfo(file);
        this.uploadFile(file);
    }

    displayFileInfo(file) {
        this.fileName.textContent = file.name;
        this.fileSize.textContent = this.formatFileSize(file.size);
        this.fileInfo.style.display = 'block';
        this.uploadArea.style.display = 'none';
        this.processBtn.disabled = false;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentFileId = result.file_id;
                if (result.preview && result.file_type !== 'pdf') {
                    this.displayPreview(result.preview);
                }
            } else {
                this.showError(result.error || 'Upload failed');
            }
        } catch (error) {
            this.showError('Upload failed: ' + error.message);
        }
    }

    displayPreview(base64Data) {
        this.previewImage.src = `data:image/png;base64,${base64Data}`;
        this.previewImage.style.display = 'block';
        this.previewContainer.querySelector('.preview-placeholder').style.display = 'none';
        this.zoomControls.style.display = 'flex';
        this.resetZoom();
    }

    adjustZoom(delta) {
        this.zoomLevel = Math.max(0.1, Math.min(3, this.zoomLevel + delta));
        this.updateZoom();
    }

    resetZoom() {
        this.zoomLevel = 1;
        this.imagePosition = { x: 0, y: 0 };
        this.updateZoom();
    }

    updateZoom() {
        const zoomPercent = Math.round(this.zoomLevel * 100);
        this.zoomLevel.textContent = `${zoomPercent}%`;
        this.previewImage.style.transform = `scale(${this.zoomLevel}) translate(${this.imagePosition.x}px, ${this.imagePosition.y}px)`;
    }

    startDrag(e) {
        if (this.zoomLevel > 1) {
            this.isDragging = true;
            this.dragStart = { x: e.clientX - this.imagePosition.x, y: e.clientY - this.imagePosition.y };
            this.previewImage.style.cursor = 'grabbing';
        }
    }

    drag(e) {
        if (this.isDragging) {
            this.imagePosition.x = e.clientX - this.dragStart.x;
            this.imagePosition.y = e.clientY - this.dragStart.y;
            this.updateZoom();
        }
    }

    endDrag() {
        this.isDragging = false;
        this.previewImage.style.cursor = 'grab';
    }

    async processFile() {
        if (!this.currentFileId) {
            this.showError('No file to process');
            return;
        }

        this.showProcessingAnimation();

        try {
            const response = await fetch('/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ file_id: this.currentFileId })
            });

            const result = await response.json();

            if (result.success) {
                this.displayResults(result);
            } else {
                this.showError(result.error || 'Processing failed');
            }
        } catch (error) {
            this.showError('Processing failed: ' + error.message);
        } finally {
            this.hideProcessingAnimation();
        }
    }

    showProcessingAnimation() {
        this.processingOverlay.style.display = 'flex';
        
        // Animate progress steps
        setTimeout(() => this.step2.classList.add('active'), 500);
        setTimeout(() => this.step3.classList.add('active'), 1500);
        setTimeout(() => this.step4.classList.add('active'), 2500);
    }

    hideProcessingAnimation() {
        this.processingOverlay.style.display = 'none';
        // Reset steps
        [this.step2, this.step3, this.step4].forEach(step => step.classList.remove('active'));
    }

    displayResults(result) {
        this.extractedText.value = result.text;
        this.processingTime.textContent = `${result.processing_time}s`;
        this.wordCount.textContent = result.word_count.toLocaleString();
        this.charCount.textContent = result.char_count.toLocaleString();
        
        this.resultsSection.style.display = 'block';
        this.resultsSection.classList.add('fade-in');
        
        // Scroll to results
        this.resultsSection.scrollIntoView({ behavior: 'smooth' });
    }

    async copyText() {
        try {
            await navigator.clipboard.writeText(this.extractedText.value);
            this.showSuccess('Text copied to clipboard!');
        } catch (error) {
            this.showError('Failed to copy text');
        }
    }

    downloadText() {
        if (!this.currentFileId) return;
        
        const link = document.createElement('a');
        link.href = `/download/${this.currentFileId}`;
        link.download = `${this.currentFile.name}_extracted.txt`;
        link.click();
    }

    removeFile() {
        this.currentFile = null;
        this.currentFileId = null;
        this.fileInfo.style.display = 'none';
        this.uploadArea.style.display = 'block';
        this.processBtn.disabled = true;
        this.previewImage.style.display = 'none';
        this.previewContainer.querySelector('.preview-placeholder').style.display = 'flex';
        this.zoomControls.style.display = 'none';
        this.resultsSection.style.display = 'none';
        this.fileInput.value = '';
    }

    resetApp() {
        this.removeFile();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    showError(message) {
        // Simple error display - you can enhance this with a proper modal
        alert('Error: ' + message);
    }

    showSuccess(message) {
        // Simple success display - you can enhance this with a proper notification
        alert(message);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new OCRApp();
});
