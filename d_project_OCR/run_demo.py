#!/usr/bin/env python3
"""
OCR Web Application Demo Launcher - Optimized for fast loading
"""
import json
import webbrowser
import time
import threading
from app_demo import app

def open_browser():
    """Open browser after a short delay"""
    time.sleep(1)  # Reduced delay for faster startup
    with open('config.json', 'r') as f:
        config = json.load(f)
    url = config['app_url']
    print(f"🌐 Opening browser at: {url}")
    webbrowser.open(url)

if __name__ == '__main__':
    # Load configuration
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    print("🚀 Starting OCR Web Application (DEMO MODE)...")
    print(f"📍 Server will run at: {config['app_url']}")
    print("🎭 Running in demo mode - OCR results will be simulated")
    print("📱 The browser will open automatically")
    print("🛑 Press Ctrl+C to stop the server")
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # Start Flask app with optimizations
    app.run(
        host=config['host'],
        port=config['port'],
        debug=False,  # Disable debug for faster loading
        threaded=True,
        use_reloader=False
    )
